import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo, useState } from 'react';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';

import {
  getCacheConfig,
  getCacheKey,
  OPTIMISTIC_CONFIG,
} from 'config/cacheConfig';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

// Tipos para Optimistic UI
interface ClienteOption {
  label: string;
  value: string;
  obj?: any;
}

interface VendedorOption {
  label: string;
  value: string;
}

// Hook para busca otimística de clientes
export const useOptimisticClientes = () => {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  // Cache de clientes recentes/populares
  const { data: clientesPopulares = [] } = useQuery({
    queryKey: getCacheKey('CLIENTES_POPULARES'),
    queryFn: async (): Promise<ClienteOption[]> => {
      const response = await api.get<void, ResponseApi<any[]>>(
        ConstanteEnderecoWebservice.CLIENTE_FORNECEDOR_LISTAR_SELECT_PDV,
        {
          params: {
            cpfCnpjNomeApelidoCodigoExterno: '',
            limit: 20, // Buscar apenas os 20 mais recentes/populares
          },
        }
      );

      if (response?.sucesso && response.dados) {
        return response.dados.map((cliente) => ({
          label: cliente.nome || cliente.descricao,
          value: cliente.id,
          obj: cliente,
        }));
      }
      return [];
    },
    ...getCacheConfig('CLIENTES_POPULARES'),
  });

  // Busca de clientes com debounce e cache
  const { data: clientesBusca = [], isFetching } = useQuery({
    queryKey: getCacheKey('CLIENTES_BUSCA', searchTerm),
    queryFn: async (): Promise<ClienteOption[]> => {
      if (!searchTerm || searchTerm.length < 2) return [];

      const response = await api.get<void, ResponseApi<any[]>>(
        ConstanteEnderecoWebservice.CLIENTE_FORNECEDOR_LISTAR_SELECT_PDV,
        {
          params: { cpfCnpjNomeApelidoCodigoExterno: searchTerm },
        }
      );

      if (response?.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
      }

      if (response?.sucesso && response.dados) {
        return response.dados.map((cliente) => ({
          label: cliente.nome || cliente.descricao,
          value: cliente.id,
          obj: cliente,
        }));
      }
      return [];
    },
    enabled: searchTerm.length >= 2,
    ...getCacheConfig('CLIENTES_BUSCA'),
  });

  // Função para busca com optimistic loading
  const buscarClientes = useCallback(
    (termo: string) => {
      setSearchTerm(termo);
      setIsSearching(true);

      // Simular resultado imediato se temos cache
      const cacheKey = ['clientes-busca', termo];
      const cachedData = queryClient.getQueryData(cacheKey);

      if (cachedData) {
        setIsSearching(false);
      }
    },
    [queryClient]
  );

  // Combinar resultados: cache primeiro, depois busca
  const clientesOptions = useMemo(() => {
    if (searchTerm.length >= 2) {
      return clientesBusca;
    }
    return clientesPopulares;
  }, [searchTerm, clientesBusca, clientesPopulares]);

  // Prefetch de cliente específico
  const prefetchCliente = useCallback(
    (clienteId: string) => {
      queryClient.prefetchQuery({
        queryKey: ['cliente-detalhes', clienteId],
        queryFn: async () => {
          const response = await api.get<void, ResponseApi<any>>(
            `${ConstanteEnderecoWebservice.CLIENTE_OBTER}/${clienteId}`
          );
          return response?.sucesso ? response.dados : null;
        },
        staleTime: 10 * 60 * 1000,
      });
    },
    [queryClient]
  );

  return {
    clientesOptions,
    isLoading: isSearching || isFetching,
    buscarClientes,
    prefetchCliente,
    clientesPopulares,
  };
};

// Hook para vendedores com cache otimizado
export const useOptimisticVendedores = () => {
  const queryClient = useQueryClient();

  const {
    data: vendedores = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['vendedores-select-optimistic'],
    queryFn: async (): Promise<VendedorOption[]> => {
      const response = await api.get<void, ResponseApi<any[]>>(
        ConstanteEnderecoWebservice.VENDEDOR_LISTAR_SELECT_POR_LOJA
      );

      if (response?.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
      }

      if (response?.sucesso && response.dados) {
        return response.dados.map((vendedor) => ({
          label: vendedor.nome,
          value: vendedor.id,
        }));
      }
      return [];
    },
    staleTime: 20 * 60 * 1000, // 20 minutos (vendedores mudam pouco)
    gcTime: 60 * 60 * 1000, // 1 hora
    retry: 2,
    refetchOnWindowFocus: false,
  });

  // Função para invalidar cache quando necessário
  const invalidarVendedores = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: ['vendedores-select-optimistic'],
    });
  }, [queryClient]);

  return {
    vendedores,
    isLoading,
    error,
    invalidarVendedores,
  };
};

// Hook para dados de configuração que raramente mudam
export const useConfiguracoesCache = () => {
  // Cache de tabelas de preço
  const { data: tabelasPreco = [] } = useQuery({
    queryKey: ['tabelas-preco-cache'],
    queryFn: async () => {
      const response = await api.get<void, ResponseApi<any[]>>(
        ConstanteEnderecoWebservice.TABELA_PRECO_LISTAR_TABELAS_PRECO
      );
      return response?.sucesso ? response.dados : [];
    },
    staleTime: 30 * 60 * 1000, // 30 minutos
    gcTime: 2 * 60 * 60 * 1000, // 2 horas
  });

  // Cache de locais de estoque
  const { data: locaisEstoque = [] } = useQuery({
    queryKey: ['locais-estoque-cache'],
    queryFn: async () => {
      const response = await api.get<void, ResponseApi<any[]>>(
        ConstanteEnderecoWebservice.LOCAL_ESTOQUE_LISTAR_SELECT
      );
      return response?.sucesso ? response.dados : [];
    },
    staleTime: 30 * 60 * 1000, // 30 minutos
    gcTime: 2 * 60 * 60 * 1000, // 2 horas
  });

  // Cache de formas de pagamento
  const { data: formasPagamento = [] } = useQuery({
    queryKey: ['formas-pagamento-cache'],
    queryFn: async () => {
      const response = await api.get<void, ResponseApi<any[]>>(
        ConstanteEnderecoWebservice.FORMA_PAGAMENTO_LISTAR_SELECT_PDV
      );
      return response?.sucesso ? response.dados : [];
    },
    staleTime: 60 * 60 * 1000, // 1 hora (raramente muda)
    gcTime: 4 * 60 * 60 * 1000, // 4 horas
  });

  return {
    tabelasPreco,
    locaisEstoque,
    formasPagamento,
  };
};
