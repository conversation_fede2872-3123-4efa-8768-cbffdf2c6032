// Configurações de cache para React Query e Optimistic UI

// Tempos em milissegundos
export const CACHE_TIMES = {
  // Dados críticos (carregamento prioritário)
  CLIENTE_PADRAO: 15 * 60 * 1000, // 15 minutos
  VENDEDORES: 20 * 60 * 1000, // 20 minutos

  // Dados de busca (atualizados frequentemente)
  CLIENTES_POPULARES: 10 * 60 * 1000, // 10 minutos
  CLIENTES_BUSCA: 5 * 60 * 1000, // 5 minutos

  // Dados de configuração (raramente mudam)
  TABELAS_PRECO: 30 * 60 * 1000, // 30 minutos
  LOCAIS_ESTOQUE: 30 * 60 * 1000, // 30 minutos
  FORMAS_PAGAMENTO: 60 * 60 * 1000, // 1 hora

  // Dados de sessão
  ULTIMO_PEDIDO: 2 * 60 * 1000, // 2 minutos
  VENDEDOR_VINCULADO: 15 * 60 * 1000, // 15 minutos

  // Dados específicos
  CLIENTE_DETALHES: 10 * 60 * 1000, // 10 minutos
} as const;

// Tempos de Garbage Collection (quando remover da memória)
export const GC_TIMES = {
  CLIENTE_PADRAO: 30 * 60 * 1000, // 30 minutos
  VENDEDORES: 60 * 60 * 1000, // 1 hora
  CLIENTES_POPULARES: 30 * 60 * 1000, // 30 minutos
  CLIENTES_BUSCA: 15 * 60 * 1000, // 15 minutos
  TABELAS_PRECO: 2 * 60 * 60 * 1000, // 2 horas
  LOCAIS_ESTOQUE: 2 * 60 * 60 * 1000, // 2 horas
  FORMAS_PAGAMENTO: 4 * 60 * 60 * 1000, // 4 horas
  ULTIMO_PEDIDO: 5 * 60 * 1000, // 5 minutos
  VENDEDOR_VINCULADO: 30 * 60 * 1000, // 30 minutos
  CLIENTE_DETALHES: 15 * 60 * 1000, // 15 minutos
} as const;

// Configurações de retry
export const RETRY_CONFIG = {
  DEFAULT: 2,
  CRITICAL: 3,
  BACKGROUND: 1,
  SEARCH: 1,
} as const;

// Configurações de loading otimístico
export const OPTIMISTIC_CONFIG = {
  MIN_LOADING_TIME: 300, // Mínimo para evitar flash
  DEBOUNCE_SEARCH: 300, // Debounce para busca
  TRANSITION_DELAY: 150, // Delay para transições suaves
  PROGRESS_UPDATE_INTERVAL: 50, // Intervalo de atualização do progresso
} as const;

// Chaves de cache padronizadas
export const CACHE_KEYS = {
  // PDV Home
  CLIENTE_PADRAO_SISTEMA: ['cliente-padrao-sistema'],
  VENDEDORES_SELECT: ['vendedores-select-optimistic'],
  ULTIMO_PEDIDO: ['ultimo-pedido-cadastrado'],
  VENDEDOR_VINCULADO: ['vendedor-vinculado'],

  // Clientes
  CLIENTES_POPULARES: ['clientes-populares'],
  CLIENTES_BUSCA: (termo: string) => ['clientes-busca', termo],
  CLIENTE_DETALHES: (id: string) => ['cliente-detalhes', id],

  // Configurações
  TABELAS_PRECO: ['tabelas-preco-cache'],
  LOCAIS_ESTOQUE: ['locais-estoque-cache'],
  FORMAS_PAGAMENTO: ['formas-pagamento-cache'],
} as const;

// Configurações por tipo de query
export const QUERY_CONFIGS = {
  // Dados críticos - carregamento prioritário
  CRITICAL: {
    staleTime: CACHE_TIMES.CLIENTE_PADRAO,
    gcTime: GC_TIMES.CLIENTE_PADRAO,
    retry: RETRY_CONFIG.CRITICAL,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  },

  // Dados de busca - atualizados com frequência
  SEARCH: {
    staleTime: CACHE_TIMES.CLIENTES_BUSCA,
    gcTime: GC_TIMES.CLIENTES_BUSCA,
    retry: RETRY_CONFIG.SEARCH,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  },

  // Dados de configuração - raramente mudam
  CONFIG: {
    staleTime: CACHE_TIMES.TABELAS_PRECO,
    gcTime: GC_TIMES.TABELAS_PRECO,
    retry: RETRY_CONFIG.DEFAULT,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  },

  // Dados de background - não críticos
  BACKGROUND: {
    staleTime: CACHE_TIMES.ULTIMO_PEDIDO,
    gcTime: GC_TIMES.ULTIMO_PEDIDO,
    retry: RETRY_CONFIG.BACKGROUND,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  },
} as const;

// Configurações específicas por endpoint
export const ENDPOINT_CONFIGS = {
  CLIENTE_PADRAO_SISTEMA: {
    ...QUERY_CONFIGS.CRITICAL,
    staleTime: CACHE_TIMES.CLIENTE_PADRAO,
    gcTime: GC_TIMES.CLIENTE_PADRAO,
  },

  VENDEDORES_SELECT: {
    ...QUERY_CONFIGS.CRITICAL,
    staleTime: CACHE_TIMES.VENDEDORES,
    gcTime: GC_TIMES.VENDEDORES,
  },

  CLIENTES_POPULARES: {
    ...QUERY_CONFIGS.SEARCH,
    staleTime: CACHE_TIMES.CLIENTES_POPULARES,
    gcTime: GC_TIMES.CLIENTES_POPULARES,
  },

  CLIENTES_BUSCA: {
    ...QUERY_CONFIGS.SEARCH,
    staleTime: CACHE_TIMES.CLIENTES_BUSCA,
    gcTime: GC_TIMES.CLIENTES_BUSCA,
  },

  TABELAS_PRECO: {
    ...QUERY_CONFIGS.CONFIG,
    staleTime: CACHE_TIMES.TABELAS_PRECO,
    gcTime: GC_TIMES.TABELAS_PRECO,
  },

  LOCAIS_ESTOQUE: {
    ...QUERY_CONFIGS.CONFIG,
    staleTime: CACHE_TIMES.LOCAIS_ESTOQUE,
    gcTime: GC_TIMES.LOCAIS_ESTOQUE,
  },

  FORMAS_PAGAMENTO: {
    ...QUERY_CONFIGS.CONFIG,
    staleTime: CACHE_TIMES.FORMAS_PAGAMENTO,
    gcTime: GC_TIMES.FORMAS_PAGAMENTO,
  },

  ULTIMO_PEDIDO: {
    ...QUERY_CONFIGS.BACKGROUND,
    staleTime: CACHE_TIMES.ULTIMO_PEDIDO,
    gcTime: GC_TIMES.ULTIMO_PEDIDO,
  },

  VENDEDOR_VINCULADO: {
    ...QUERY_CONFIGS.BACKGROUND,
    staleTime: CACHE_TIMES.VENDEDOR_VINCULADO,
    gcTime: GC_TIMES.VENDEDOR_VINCULADO,
  },

  CLIENTE_DETALHES: {
    ...QUERY_CONFIGS.SEARCH,
    staleTime: CACHE_TIMES.CLIENTE_DETALHES,
    gcTime: GC_TIMES.CLIENTE_DETALHES,
  },
} as const;

// Utilitários para configuração
export const getCacheConfig = (endpoint: keyof typeof ENDPOINT_CONFIGS) => {
  return ENDPOINT_CONFIGS[endpoint];
};

export const getCacheKey = (key: keyof typeof CACHE_KEYS, ...params: any[]) => {
  const keyFactory = CACHE_KEYS[key];
  if (typeof keyFactory === 'function') {
    return (keyFactory as (...args: any[]) => any)(...params);
  }
  return keyFactory;
};

// Configurações de desenvolvimento vs produção
export const isDevelopment = process.env.NODE_ENV === 'development';

export const DEV_OVERRIDES = isDevelopment
  ? {
      // Em desenvolvimento, cache mais curto para facilitar testes
      staleTime: 30 * 1000, // 30 segundos
      gcTime: 60 * 1000, // 1 minuto
    }
  : {};

export default {
  CACHE_TIMES,
  GC_TIMES,
  RETRY_CONFIG,
  OPTIMISTIC_CONFIG,
  CACHE_KEYS,
  QUERY_CONFIGS,
  ENDPOINT_CONFIGS,
  getCacheConfig,
  getCacheKey,
  DEV_OVERRIDES,
};
