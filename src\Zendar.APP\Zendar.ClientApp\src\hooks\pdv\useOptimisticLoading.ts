import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';

interface OptimisticState {
  isInitialLoading: boolean;
  isOptimisticReady: boolean;
  isFullyReady: boolean;
  progress: number;
}

interface UseOptimisticLoadingProps {
  criticalQueries: string[][];
  backgroundQueries?: string[][];
  minLoadingTime?: number;
}

// Hook para gerenciar estados de loading otimístico
export const useOptimisticLoading = ({
  criticalQueries,
  backgroundQueries = [],
  minLoadingTime = 500,
}: UseOptimisticLoadingProps) => {
  const queryClient = useQueryClient();
  const [state, setState] = useState<OptimisticState>({
    isInitialLoading: true,
    isOptimisticReady: false,
    isFullyReady: false,
    progress: 0,
  });
  const [startTime] = useState(Date.now());

  // Verificar se queries críticas estão prontas
  const checkCriticalQueries = useCallback(() => {
    const criticalResults = criticalQueries.map((queryKey) => {
      const query = queryClient.getQueryState(queryKey);
      return {
        hasData: !!query?.data,
        isLoading: query?.fetchStatus === 'fetching',
        isError: query?.status === 'error',
      };
    });

    const criticalReady = criticalResults.every(
      (result) => result.hasData || result.isError
    );
    const criticalLoading = criticalResults.some((result) => result.isLoading);

    return { criticalReady, criticalLoading };
  }, [criticalQueries, queryClient]);

  // Verificar se queries de background estão prontas
  const checkBackgroundQueries = useCallback(() => {
    if (backgroundQueries.length === 0) return { backgroundReady: true };

    const backgroundResults = backgroundQueries.map((queryKey) => {
      const query = queryClient.getQueryState(queryKey);
      return {
        hasData: !!query?.data,
        isLoading: query?.fetchStatus === 'fetching',
        isError: query?.status === 'error',
      };
    });

    const backgroundReady = backgroundResults.every(
      (result) => result.hasData || result.isError
    );

    return { backgroundReady };
  }, [backgroundQueries, queryClient]);

  // Calcular progresso baseado no estado das queries
  const calculateProgress = useCallback(() => {
    const totalQueries = criticalQueries.length + backgroundQueries.length;
    if (totalQueries === 0) return 100;

    let completedQueries = 0;

    // Contar queries críticas completas
    criticalQueries.forEach((queryKey) => {
      const query = queryClient.getQueryState(queryKey);
      if (query?.data || query?.status === 'error') {
        completedQueries += 1;
      }
    });

    // Contar queries de background completas
    backgroundQueries.forEach((queryKey) => {
      const query = queryClient.getQueryState(queryKey);
      if (query?.data || query?.status === 'error') {
        completedQueries += 1;
      }
    });

    return Math.round((completedQueries / totalQueries) * 100);
  }, [criticalQueries, backgroundQueries, queryClient]);

  // Atualizar estado baseado nas queries
  const updateState = useCallback(() => {
    const { criticalReady, criticalLoading } = checkCriticalQueries();
    const { backgroundReady } = checkBackgroundQueries();
    const progress = calculateProgress();
    const elapsedTime = Date.now() - startTime;

    setState((prevState) => {
      const newState: OptimisticState = {
        ...prevState,
        progress,
      };

      // Fase 1: Loading inicial (mostra skeleton completo)
      if (criticalLoading && elapsedTime < minLoadingTime) {
        newState.isInitialLoading = true;
        newState.isOptimisticReady = false;
        newState.isFullyReady = false;
      }
      // Fase 2: Optimistic ready (dados críticos prontos, pode mostrar UI)
      else if (criticalReady && elapsedTime >= minLoadingTime) {
        newState.isInitialLoading = false;
        newState.isOptimisticReady = true;
        newState.isFullyReady = backgroundReady;
      }
      // Fase 3: Fully ready (todos os dados prontos)
      else if (criticalReady && backgroundReady) {
        newState.isInitialLoading = false;
        newState.isOptimisticReady = true;
        newState.isFullyReady = true;
      }

      return newState;
    });
  }, [
    checkCriticalQueries,
    checkBackgroundQueries,
    calculateProgress,
    startTime,
    minLoadingTime,
  ]);

  // Observar mudanças nas queries
  useEffect(() => {
    updateState();

    // Configurar observador para mudanças nas queries
    const unsubscribe = queryClient.getQueryCache().subscribe(() => {
      updateState();
    });

    return unsubscribe;
  }, [updateState, queryClient]);

  // Funções de conveniência
  const shouldShowSkeleton = state.isInitialLoading;
  const shouldShowOptimisticUI = state.isOptimisticReady && !state.isFullyReady;
  const canInteract = state.isOptimisticReady;

  return {
    ...state,
    shouldShowSkeleton,
    shouldShowOptimisticUI,
    canInteract,
  };
};

// Hook específico para o PDV Home
export const useOptimisticPdvHome = () => {
  return useOptimisticLoading({
    criticalQueries: [
      ['cliente-padrao-sistema'],
      ['vendedores-select-optimistic'],
    ],
    backgroundQueries: [
      ['ultimo-pedido-cadastrado'],
      ['vendedor-vinculado'],
      ['tabelas-preco-cache'],
      ['locais-estoque-cache'],
    ],
    minLoadingTime: 300, // Mínimo 300ms para evitar flash
  });
};

// Hook para transições suaves entre estados
export const useOptimisticTransition = (isReady: boolean, delay = 150) => {
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    if (isReady) {
      const timer = setTimeout(() => setShouldRender(true), delay);
      return () => clearTimeout(timer);
    } else {
      setShouldRender(false);
    }
  }, [isReady, delay]);

  return shouldRender;
};

// Hook para feedback visual de progresso
export const useProgressFeedback = (progress: number) => {
  const [displayProgress, setDisplayProgress] = useState(0);

  useEffect(() => {
    // Animar progresso suavemente
    const timer = setTimeout(() => {
      setDisplayProgress(progress);
    }, 50);

    return () => clearTimeout(timer);
  }, [progress]);

  // Determinar cor baseada no progresso
  const getProgressColor = useCallback(() => {
    if (displayProgress < 30) return 'red.400';
    if (displayProgress < 70) return 'yellow.400';
    return 'green.400';
  }, [displayProgress]);

  // Determinar mensagem baseada no progresso
  const getProgressMessage = useCallback(() => {
    if (displayProgress < 25) return 'Carregando dados...';
    if (displayProgress < 50) return 'Preparando interface...';
    if (displayProgress < 75) return 'Quase pronto...';
    if (displayProgress < 100) return 'Finalizando...';
    return 'Pronto!';
  }, [displayProgress]);

  return {
    displayProgress,
    progressColor: getProgressColor(),
    progressMessage: getProgressMessage(),
  };
};
